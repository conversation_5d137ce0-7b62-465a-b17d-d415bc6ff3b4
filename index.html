<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=no, interactive-widget=resizes-content"
    />
    <title>SOCAR - Suivi & Départs</title>
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent"
    />
    <meta name="theme-color" content="#f1f5f9" />
    <meta name="overscroll-behavior" content="none" />
    <script src="https://cdn.tailwindcss.com"></script>
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
    <style>
      @import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");
      * {
        font-family: "Poppins", sans-serif;
        -webkit-tap-highlight-color: transparent;
      }
      [x-cloak] {
        display: none !important;
      }
      html,
      body {
        height: 100%;
        overscroll-behavior: none;
        touch-action: manipulation;
        background-color: #f1f5f9;
      }
      .card {
        background-color: white;
        border-radius: 1.25rem;
        box-shadow: 0 10px 25px -5px rgba(27, 39, 59, 0.05),
          0 8px 10px -6px rgba(27, 39, 59, 0.05);
      }
      .sidebar {
        background: linear-gradient(145deg, #0f172a 0%, #1e293b 50%, #334155 100%);
        border-right: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 4px 0 20px -5px rgba(0, 0, 0, 0.3);
      }
      .sidebar-icon {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 56px;
        height: 56px;
        border-radius: 16px;
        color: #94a3b8;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
      }
      .sidebar-icon:hover {
        background: rgba(59, 130, 246, 0.2);
        border-color: rgba(59, 130, 246, 0.3);
        color: #e2e8f0;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px -5px rgba(59, 130, 246, 0.4);
      }
      .sidebar-icon.active {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
        border-color: rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 25px -5px rgba(59, 130, 246, 0.6);
        transform: translateY(-1px);
        animation: gentle-pulse 2s infinite;
      }
      @keyframes gentle-pulse {
        0%, 100% { box-shadow: 0 8px 25px -5px rgba(59, 130, 246, 0.6); }
        50% { box-shadow: 0 12px 35px -5px rgba(59, 130, 246, 0.8); }
      }
      .sidebar-icon.active::before {
        content: "";
        position: absolute;
        left: -1.25rem;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 32px;
        background: linear-gradient(to bottom, #3b82f6, #1d4ed8);
        border-radius: 0 8px 8px 0;
        box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
        animation: side-glow 2s infinite;
      }
      @keyframes side-glow {
        0%, 100% { box-shadow: 0 0 10px rgba(59, 130, 246, 0.5); }
        50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8); }
      }
      /* Enhanced focus states for accessibility */
      .sidebar-icon:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
        transform: translateY(-2px);
      }
      /* Ripple effect on click */
      .sidebar-icon {
        position: relative;
        overflow: hidden;
      }
      .sidebar-icon::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        transition: width 0.6s ease, height 0.6s ease;
      }
      .sidebar-icon:active::after {
        width: 120px;
        height: 120px;
      }
      /* Logo hover effects */
      .sidebar .group img {
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
      /* Language switcher enhancements */
      .sidebar button:hover {
        transform: scale(1.05) translateY(-1px);
      }
      /* Sidebar responsiveness */
      @media (max-width: 768px) {
        .sidebar {
          width: 80px;
        }
        .sidebar-icon {
          width: 48px;
          height: 48px;
        }
        .tooltip {
          font-size: 0.75rem;
          padding: 6px 10px;
        }
      }
      /* Tooltip styles */
      .tooltip {
        position: absolute;
        left: 70px;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(15, 23, 42, 0.95);
        color: white;
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 500;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        z-index: 50;
      }
      .tooltip::before {
        content: "";
        position: absolute;
        left: -4px;
        top: 50%;
        transform: translateY(-50%);
        border: 4px solid transparent;
        border-right-color: rgba(15, 23, 42, 0.95);
      }
      .sidebar-icon:hover .tooltip {
        opacity: 1;
        visibility: visible;
        transform: translateY(-50%) translateX(8px);
      }
      .btn-primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        box-shadow: 0 4px 15px -3px rgba(59, 130, 246, 0.4);
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        color: white;
        font-weight: 600;
      }
      .btn-primary:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px -5px rgba(59, 130, 246, 0.5);
      }
      .btn-primary:disabled {
        background: #94a3b8;
        box-shadow: none;
        cursor: not-allowed;
        opacity: 0.7;
      }
      /* Enhanced VIN input focus styles for better visibility */
      .vin-input-box {
        transition: all 0.2s ease-in-out;
        position: relative;
      }
      .vin-input-box.focused {
        border-color: #2563eb !important;
        border-width: 3px !important;
        box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.2), 0 8px 25px -5px rgba(37, 99, 235, 0.3) !important;
        background-color: #ffffff !important;
        transform: scale(1.05) !important;
      }
      .vin-input-box.focused::after {
        content: "";
        position: absolute;
        inset: -2px;
        border-radius: 10px;
        background: linear-gradient(45deg, #2563eb, #3b82f6);
        z-index: -1;
        opacity: 0.3;
      }
      .vin-cursor {
        background: linear-gradient(45deg, #2563eb, #3b82f6);
        animation: pulse-strong 1s infinite;
        width: 2px !important;
        height: 32px !important;
      }
      @keyframes pulse-strong {
        0%, 100% { opacity: 1; transform: scaleY(1); }
        50% { opacity: 0.3; transform: scaleY(0.8); }
      }
      #idle-screen video {
        position: absolute;
        top: 50%;
        left: 50%;
        min-width: 100%;
        min-height: 100%;
        width: auto;
        height: auto;
        z-index: -100;
        transform: translateX(-50%) translateY(-50%);
        background-size: cover;
      }
    </style>
  </head>
  <body class="text-slate-700 antialiased" x-data>
    <!-- State: Idle Screen -->
    <div
      id="idle-screen"
      x-data="idle"
      x-show="$store.app.isIdle"
      x-transition.opacity.duration.500ms
      x-cloak
      class="fixed inset-0 bg-slate-900 overflow-hidden"
    >
      <video
        x-ref="video"
        muted
        loop
        playsinline
        src="assets/videos/socar-promo.mp4"
      ></video>
      <div class="absolute inset-0 bg-black/40"></div>
      <div
        class="relative h-full flex flex-col items-center justify-center text-white text-center p-8"
      >
        <img
          src="/assets/images/socar-logo.png"
          alt="SOCAR Logo"
          class="w-24 mb-6"
        />
        <h1 class="text-5xl font-bold" x-text="$store.app.t('idle_title')"></h1>
        <p
          class="text-2xl mt-2 opacity-80"
          x-text="$store.app.t('idle_subtitle')"
        ></p>
        <div
          class="mt-12 border-2 border-white/50 rounded-full p-6 animate-pulse"
        >
          <p
            class="text-xl font-semibold"
            x-text="$store.app.t('touch_to_start')"
          ></p>
        </div>
      </div>
    </div>

    <!-- Main Application UI -->
    <div x-show="!$store.app.isIdle" class="flex h-screen overflow-hidden" x-cloak>
      <!-- Sidebar -->
      <aside
        class="sidebar w-28 flex-shrink-0 p-5 flex flex-col items-center justify-between relative"
      >
        <!-- Decorative gradient overlay -->
        <div class="absolute inset-0 bg-gradient-to-b from-slate-800/50 to-transparent pointer-events-none rounded-r-3xl"></div>
        
        <div class="flex flex-col items-center space-y-10 w-full relative z-10">
          <!-- Logo with enhanced styling -->
          <a href="#" @click.prevent="$store.app.setView('search')" class="group">
            <div class="w-16 h-16 bg-gradient-to-br from-white to-slate-50 rounded-2xl flex items-center justify-center shadow-xl group-hover:shadow-2xl transition-all duration-300 group-hover:scale-110 border border-white/20">
              <img
                src="/assets/images/socar-logo.png"
                alt="SOCAR Logo"
                class="w-10 transition-transform duration-300 group-hover:scale-110"
              />
            </div>
          </a>
          
          <nav class="flex flex-col items-center space-y-6 w-full">
            <!-- Search/Vehicle Tracking -->
            <a
              href="#"
              @click.prevent="$store.app.setView('search')"
              class="sidebar-icon group"
              :class="{'active': ['search', 'list', 'detail'].includes($store.app.currentView)}"
              title="Vehicle Search"
            >
              <i class='bx bx-search-alt-2 text-2xl transition-transform duration-300 group-hover:scale-110'></i>
              <div class="tooltip">Vehicle Search</div>
            </a>
            
            <!-- Departures/Shipping -->
            <a
              href="#"
              @click.prevent="$store.app.setView('departuresList')"
              class="sidebar-icon group"
              :class="{'active': ['departuresList', 'destinationDepartures'].includes($store.app.currentView)}"
              title="Shipping Schedules"
            >
              <i class='bx bxs-ship text-2xl transition-transform duration-300 group-hover:scale-110'></i>
              <div class="tooltip">Shipping Schedules</div>
            </a>
            
            <!-- Marketplace -->
            <a
              href="#"
              @click.prevent="$store.app.setView('marketplace')"
              class="sidebar-icon group"
              :class="{'active': $store.app.currentView === 'marketplace'}"
              title="Vehicle Marketplace"
            >
              <i class='bx bx-store-alt text-2xl transition-transform duration-300 group-hover:scale-110'></i>
              <div class="tooltip">Marketplace</div>
            </a>
            
            <!-- Help & Support -->
            <a
              href="#"
              @click.prevent="$store.app.setView('help')"
              class="sidebar-icon group"
              :class="{'active': $store.app.currentView === 'help'}"
              title="Help & Support"
            >
              <i class='bx bx-help-circle text-2xl transition-transform duration-300 group-hover:scale-110'></i>
              <div class="tooltip">Help & Support</div>
            </a>
          </nav>
        </div>
        
        <!-- Language switcher with enhanced design -->
        <div class="flex flex-col items-center space-y-4 w-full relative z-10">
          <!-- Divider line -->
          <div class="w-8 h-px bg-slate-600 opacity-50"></div>
          
          <div class="bg-slate-800/50 backdrop-blur-sm rounded-xl p-2 border border-slate-600/30">
            <div class="flex flex-col gap-2">
              <button
                @click="$store.app.setLanguage('fr')"
                class="w-12 h-8 text-sm font-bold transition-all duration-300 rounded-lg flex items-center justify-center group"
                :class="{ 
                  'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg transform scale-105': $store.app.currentLang === 'fr', 
                  'text-slate-300 hover:bg-slate-700/50 hover:text-white hover:scale-105': $store.app.currentLang !== 'fr' 
                }"
              >
                FR
              </button>
              <button
                @click="$store.app.setLanguage('en')"
                class="w-12 h-8 text-sm font-bold transition-all duration-300 rounded-lg flex items-center justify-center group"
                :class="{ 
                  'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg transform scale-105': $store.app.currentLang === 'en', 
                  'text-slate-300 hover:bg-slate-700/50 hover:text-white hover:scale-105': $store.app.currentLang !== 'en' 
                }"
              >
                EN
              </button>
            </div>
          </div>
        </div>
      </aside>

      <!-- Main Content -->
      <main class="flex-1 overflow-y-auto p-6 lg:p-8">
        <!-- View 1: Search Form -->
        <div
          x-show="$store.app.currentView === 'search'"
          x-data="search"
          x-transition:enter="transition ease-out duration-300"
          x-transition:enter-start="opacity-0"
          x-transition:enter-end="opacity-100"
          class="h-full flex flex-col justify-center items-center"
        >
          <div class="card max-w-4xl mx-auto p-8 lg:p-12 text-center w-full">
            <h1
              class="text-3xl lg:text-4xl font-bold text-slate-800"
              x-text="$store.app.t('main_title')"
            ></h1>
            <p
              class="text-slate-500 mt-2"
              x-text="$store.app.t('main_subtitle')"
            ></p>
            <div class="flex flex-col items-center justify-center mt-8 mb-4">
              <div class="h-10 mb-4 flex items-center" x-ref="inputContainer">
                <div
                  x-show="showInvalidChar"
                  x-transition
                  class="bg-red-100 text-red-700 rounded-lg px-4 py-2 text-sm font-medium"
                  x-text="$store.app.t('invalid_char_msg')"
                ></div>
              </div>
              <input
                type="text"
                x-ref="hiddenInput"
                @input="handleInput"
                @keydown.backspace="handleBackspace"
                @paste="handlePaste"
                class="absolute opacity-0 w-0 h-0"
                maxlength="6"
              />
              <div class="flex items-center gap-3">
                <div class="flex gap-2">
                  <template x-for="char in prefix"
                    ><div
                      class="w-12 h-16 rounded-lg flex items-center justify-center text-xl font-mono font-bold bg-slate-100 text-slate-400"
                      x-text="char"
                    ></div
                  ></template>
                </div>
                <div class="mx-2 text-slate-300 text-3xl font-light">-</div>
                <div class="flex gap-2">
                  <template x-for="(char, index) in userInput"
                    ><div
                      class="vin-input-box w-12 h-16 rounded-lg flex items-center justify-center text-xl font-mono font-bold bg-slate-50 border-2 border-slate-200"
                      :class="{ 
                        'focused': index === currentIndex, 
                        '!bg-white !text-slate-800 !border-slate-300': char, 
                        '!border-red-500 !shadow-red-200': errorIndex === index 
                      }"
                      @click="setCurrentIndex(index)"
                    >
                      <span x-text="char"></span>
                      <div
                        x-show="index === currentIndex && !char"
                        class="vin-cursor"
                      ></div></div
                  ></template>
                </div>
                <button
                  x-show="userInputString.length > 0"
                  @click="clearAll"
                  x-transition
                  class="w-16 h-16 rounded-lg flex items-center justify-center text-slate-400 hover:bg-red-50 hover:text-red-500 ml-3"
                  :title="$store.app.t('clear_all')"
                >
                  <svg
                    class="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H9a1 1 0 00-1 1v3M4 7h16"
                    ></path>
                  </svg>
                </button>
              </div>
            </div>
            <button
              @click="searchVehicle"
              :disabled="userInputString.length < 6 || isSearching"
              class="btn-primary w-full max-w-md mx-auto py-4 px-8 rounded-xl text-lg"
            >
              <span
                x-show="!isSearching"
                class="flex items-center justify-center"
                ><svg
                  class="w-5 h-5 mr-3"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  ></path></svg
                ><span x-text="$store.app.t('search_button')"></span></span
              ><span
                x-show="isSearching"
                class="flex items-center justify-center"
                ><svg
                  class="animate-spin w-5 h-5 mr-3"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  ></circle>
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path></svg
                ><span x-text="$store.app.t('searching')"></span
              ></span>
            </button>
            <div class="mt-6 min-h-[48px] flex items-center justify-center">
              <div
                x-show="showError || showConnectionError"
                x-transition
                class="bg-red-100 text-red-700 rounded-lg p-3 text-sm font-medium"
              >
                <span x-show="showError"
                  ><span x-text="$store.app.t('vehicle_not_found')"></span>
                  <strong x-text="lastSearchedQuery"></strong>.</span
                ><span
                  x-show="showConnectionError"
                  x-text="$store.app.t('connection_error')"
                ></span>
              </div>
            </div>
          </div>
        </div>

        <!-- View 2: List View -->
        <div
          x-show="$store.app.currentView === 'list'"
          x-data="vehicleList"
          x-transition:enter="transition ease-out duration-300"
          x-transition:enter-start="opacity-0"
          x-transition:enter-end="opacity-100"
        >
          <header class="flex items-center justify-between mb-6">
            <h1 class="text-2xl font-bold text-slate-800">
              <span x-text="$store.app.vehicles.length"></span>
              <span x-text="$store.app.t('results_found')"></span>
            </h1>
            <button
              @click="$store.app.setView('search')"
              class="btn-primary py-2 px-5 rounded-lg text-sm flex items-center gap-2"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                /></svg
              ><span x-text="$store.app.t('new_search')"></span>
            </button>
          </header>
          <div class="grid grid-cols-1 xl:grid-cols-2 gap-6">
            <template
              x-for="vehicle in $store.app.vehicles"
              :key="vehicle.chassis"
            >
              <div
                @click="showVehicleDetails(vehicle)"
                class="card p-6 cursor-pointer hover:ring-2 hover:ring-blue-500 transition-all duration-200 flex flex-col group hover:shadow-xl"
              >
                <div class="flex items-start justify-between mb-6">
                  <div class="flex items-center gap-4">
                    <div class="relative">
                      <img
                        :src="getBrandLogo(vehicle.description)"
                        @error="$event.target.src='./assets/images/logos/default.png'"
                        class="h-12 w-auto"
                      />
                    </div>
                    <div>
                      <h3
                        class="font-bold text-slate-800 text-xl mb-1"
                        x-text="vehicle.description"
                      ></h3>
                      <!-- Improved VIN Display -->
                      <div
                        class="bg-slate-100 px-3 py-1.5 rounded-lg border border-slate-200 inline-block"
                      >
                        <span
                          class="text-sm font-mono text-slate-500"
                          x-text="vehicle.chassis.slice(0, -6)"
                        ></span>
                        <span
                          class="text-sm font-mono font-bold text-blue-600 bg-blue-100 px-2 py-0.5 rounded ml-1"
                          x-text="vehicle.chassis.slice(-6)"
                        ></span>
                      </div>
                    </div>
                  </div>
                  <div class="flex flex-col items-end gap-3">
                    <div
                      class="flex items-center gap-2 bg-slate-100 px-3 py-1.5 rounded-full"
                    >
                      <img
                        :src="getCountryFlag(vehicle.Destination)"
                        class="w-5 h-auto rounded-sm"
                        :alt="vehicle.Destination"
                      />
                      <span
                        class="text-sm font-medium text-slate-600"
                        x-text="vehicle.Destination"
                      ></span>
                    </div>
                    <!-- Enhanced Status Badge with Animation -->
                    <div
                      class="flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-semibold"
                      :class="{
                                             'bg-emerald-100 text-emerald-800': vehicle.Status === 'DEPARTED', 
                                             'bg-amber-100 text-amber-800': vehicle.Status === 'PORT OF LOADING', 
                                             'bg-blue-100 text-blue-800': vehicle.Status === 'BOOKED'
                                         }"
                    >
                      <div
                        class="w-2 h-2 rounded-full"
                        :class="{
                                                 'bg-emerald-500': vehicle.Status === 'DEPARTED', 
                                                 'bg-amber-500': vehicle.Status === 'PORT OF LOADING', 
                                                 'bg-blue-500': vehicle.Status === 'BOOKED'
                                             }"
                      ></div>
                      <span
                        x-text="$store.app.t(vehicle.Status.toLowerCase().replace(/ /g, '_'))"
                      ></span>
                    </div>
                  </div>
                </div>

                <!-- Enhanced Consignee Section -->
                <div
                  class="bg-gradient-to-r from-slate-50 to-slate-100 p-4 rounded-xl text-sm mb-6 border-l-4 border-blue-500"
                >
                  <div class="flex items-center gap-2 mb-1">
                    <svg
                      class="w-4 h-4 text-blue-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                      />
                    </svg>
                    <span
                      class="text-slate-500 font-medium"
                      x-text="$store.app.t('consignee')"
                    ></span>
                  </div>
                  <strong
                    class="text-slate-800 text-base"
                    x-text="parseConsignee(vehicle.consignee).name"
                  ></strong>
                </div>

                <!-- Completely Redesigned Timeline -->
                <div class="mt-auto">
                  <div class="mb-4">
                    <span
                      class="text-xs font-semibold text-slate-500 uppercase tracking-wider"
                      >Transport Progress</span
                    >
                  </div>

                  <!-- Modern Timeline with Icons and Animations -->
                  <div class="relative">
                    <!-- Background Track -->
                    <div
                      class="absolute top-6 left-0 right-0 h-1 bg-slate-200 rounded-full"
                    ></div>
                    <!-- Progress Track with Gradient -->
                    <div
                      class="absolute top-6 left-0 h-1 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-full transition-all duration-1000 ease-out transform origin-left"
                      :style="{ width: getProgressPercentage(vehicle.Status) + '%' }"
                    ></div>

                    <!-- Timeline Steps -->
                    <div class="relative flex justify-between">
                      <!-- Step 1: Booked -->
                      <div class="flex flex-col items-center">
                        <div
                          class="w-12 h-12 rounded-xl flex items-center justify-center mb-2 transition-all duration-300 shadow-lg"
                          :class="getTimelineStepClass(vehicle.Status, 'BOOKED')"
                        >
                          <svg
                            class="w-6 h-6"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>
                        </div>
                        <p
                          class="text-xs font-semibold"
                          :class="getTimelineTextClass(vehicle.Status, 'BOOKED')"
                          x-text="$store.app.t('booked')"
                        ></p>
                      </div>

                      <!-- Step 2: Port of Loading -->
                      <div class="flex flex-col items-center">
                        <div
                          class="w-12 h-12 rounded-xl flex items-center justify-center mb-2 transition-all duration-300 shadow-lg"
                          :class="getTimelineStepClass(vehicle.Status, 'PORT OF LOADING')"
                        >
                          <svg
                            class="w-6 h-6"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
                            />
                          </svg>
                        </div>
                        <p
                          class="text-xs font-semibold"
                          :class="getTimelineTextClass(vehicle.Status, 'PORT OF LOADING')"
                          x-text="$store.app.t('port_of_loading')"
                        ></p>
                      </div>

                      <!-- Step 3: Departed -->
                      <div class="flex flex-col items-center">
                        <div
                          class="w-12 h-12 rounded-xl flex items-center justify-center mb-2 transition-all duration-300 shadow-lg"
                          :class="getTimelineStepClass(vehicle.Status, 'DEPARTED')"
                        >
                          <svg
                            class="w-6 h-6"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                            />
                          </svg>
                        </div>
                        <p
                          class="text-xs font-semibold"
                          :class="getTimelineTextClass(vehicle.Status, 'DEPARTED')"
                          x-text="$store.app.t('departed')"
                        ></p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>

        <!-- View 3: Detail View -->
        <div
          x-show="$store.app.currentView === 'detail' && $store.app.selectedVehicle"
          x-data="vehicleDetail"
          class="flex flex-col h-full"
          x-transition:enter="transition ease-out duration-300"
          x-transition:enter-start="opacity-0"
          x-transition:enter-end="opacity-100"
        >
          <header
            class="sticky top-0 z-10 bg-slate-50/95 backdrop-blur-sm -mx-8 -mt-8 mb-6 px-8 pt-8 pb-4 border-b border-slate-200/60 flex-shrink-0"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-4">
                <button
                  @click="$store.app.setView('list')"
                  class="bg-white p-2.5 rounded-lg shadow-sm hover:bg-slate-200 text-slate-600"
                >
                  <svg
                    class="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M10 19l-7-7m0 0l7-7m-7 7h18"
                    ></path>
                  </svg>
                </button>
                <img
                  :src="getBrandLogo($store.app.selectedVehicle?.description)"
                  @error="$event.target.src='./assets/images/logos/default.png'"
                  class="h-12 w-auto"
                />
                <div>
                  <h1
                    class="text-2xl font-bold text-slate-800"
                    x-text="$store.app.selectedVehicle?.description"
                  ></h1>
                  <!-- Enhanced VIN Display -->
                  <div
                    class="bg-white px-4 py-2 rounded-xl border-2 border-slate-300 shadow-sm mt-2 inline-block"
                  >
                    <span
                      class="text-lg font-mono text-slate-600 font-medium"
                      x-text="$store.app.selectedVehicle?.chassis.slice(0, -6)"
                    ></span>
                    <span
                      class="text-lg font-mono font-bold text-white bg-blue-600 px-3 py-1 rounded-lg ml-2 shadow-md"
                      x-text="$store.app.selectedVehicle?.chassis.slice(-6)"
                    ></span>
                  </div>
                </div>
              </div>
              <div class="flex items-center gap-4">
                <div
                  class="flex items-center gap-2 bg-white px-4 py-2 rounded-full shadow-sm border border-slate-200"
                >
                  <img
                    :src="getCountryFlag($store.app.selectedVehicle?.Destination)"
                    class="w-6 h-auto rounded-sm"
                    :alt="$store.app.selectedVehicle?.Destination"
                  />
                  <span
                    class="text-sm font-medium text-slate-700"
                    x-text="$store.app.selectedVehicle?.Destination"
                  ></span>
                </div>
                <div
                  class="flex items-center gap-2 px-4 py-2 rounded-full text-sm font-semibold animate-pulse"
                  :class="{
                         'bg-emerald-100 text-emerald-800': $store.app.selectedVehicle?.Status === 'DEPARTED', 
                         'bg-amber-100 text-amber-800': $store.app.selectedVehicle?.Status === 'PORT OF LOADING', 
                         'bg-blue-100 text-blue-800': $store.app.selectedVehicle?.Status === 'BOOKED'
                     }"
                >
                  <div
                    class="w-2 h-2 rounded-full"
                    :class="{
                             'bg-emerald-500': $store.app.selectedVehicle?.Status === 'DEPARTED', 
                             'bg-amber-500': $store.app.selectedVehicle?.Status === 'PORT OF LOADING', 
                             'bg-blue-500': $store.app.selectedVehicle?.Status === 'BOOKED'
                         }"
                  ></div>
                  <span
                    x-text="$store.app.t($store.app.selectedVehicle?.Status.toLowerCase().replace(/ /g, '_'))"
                  ></span>
                </div>
              </div>
            </div>
          </header>

          <div class="flex-1 overflow-y-auto pb-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <!-- Left Column -->
              <div class="lg:col-span-2 space-y-6">
                <!-- Compact Shipping Route -->
                <div class="card p-6">
                  <h3 class="font-bold text-lg text-slate-800 mb-4">
                    Itinéraire Maritime
                  </h3>
                  <div class="flex items-center justify-between relative">
                    <div
                      class="absolute top-1/2 left-1/4 right-1/4 h-0.5 bg-slate-300 -translate-y-1/2"
                    ></div>

                    <div class="text-center z-10">
                      <div
                        class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-2"
                      >
                        <img
                          src="https://flagcdn.com/w40/be.png"
                          class="w-6 h-auto rounded-sm"
                          alt="Belgium"
                        />
                      </div>
                      <p class="font-semibold text-slate-800">Anvers</p>
                      <p
                        class="text-xs text-slate-500"
                        x-text="$store.app.selectedVehicle?.Depart"
                      ></p>
                    </div>

                    <div class="text-center z-10 bg-white">
                      <div
                        class="w-12 h-12 bg-amber-100 border-2 border-amber-300 rounded-lg flex items-center justify-center mx-auto mb-2"
                      >
                        <svg
                          class="w-6 h-6 text-amber-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                          />
                        </svg>
                      </div>
                      <p
                        class="font-semibold text-slate-800 text-sm"
                        x-text="$store.app.selectedVehicle?.Bateau"
                      ></p>
                      <span
                        x-show="$store.app.selectedVehicle?.Status !== 'DEPARTED'"
                        class="bg-amber-100 text-amber-700 text-xs px-2 py-0.5 rounded"
                        x-text="$store.app.t('planned_badge')"
                      ></span>
                    </div>

                    <div class="text-center z-10">
                      <div
                        class="w-12 h-12 bg-white border-2 border-slate-300 rounded-lg flex items-center justify-center mx-auto mb-2"
                      >
                        <img
                          :src="getCountryFlag($store.app.selectedVehicle?.Destination)"
                          class="w-6 h-auto rounded-sm"
                          :alt="$store.app.selectedVehicle?.Destination"
                        />
                      </div>
                      <p
                        class="font-semibold text-slate-800"
                        x-text="$store.app.selectedVehicle?.Destination"
                      ></p>
                      <p
                        class="text-xs text-slate-500"
                        x-text="$store.app.selectedVehicle?.Arrivee"
                      ></p>
                    </div>
                  </div>
                </div>

                <!-- Compact Partners -->
                <div class="card p-6">
                  <h3 class="font-bold text-lg text-slate-800 mb-4">
                    Partenaires Logistiques
                  </h3>
                  <div class="grid grid-cols-2 gap-4">
                    <div
                      class="bg-blue-50 p-3 rounded-lg border-l-4 border-blue-500"
                    >
                      <p class="text-xs text-blue-600 font-semibold mb-1">
                        DESTINATAIRE
                      </p>
                      <p
                        class="font-bold text-slate-800 text-sm"
                        x-text="parseConsignee($store.app.selectedVehicle?.consignee).name"
                      ></p>
                    </div>
                    <div
                      class="bg-slate-50 p-3 rounded-lg border-l-4 border-slate-500"
                    >
                      <p class="text-xs text-slate-600 font-semibold mb-1">
                        AGENT MARITIME
                      </p>
                      <p
                        class="font-bold text-slate-800 text-sm"
                        x-text="$store.app.selectedVehicle?.agent"
                      ></p>
                      <p
                        class="text-xs text-slate-500 font-mono"
                        x-text="$store.app.selectedVehicle?.agent_Tel"
                      ></p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Right Column -->
              <div class="space-y-6">
                <!-- Vertical Timeline with Connecting Lines -->
                <div class="card p-6">
                  <h3 class="font-bold text-lg text-slate-800 mb-4">
                    Suivi du Statut
                  </h3>
                  <div class="relative">
                    <template
                      x-for="(step, index) in $store.app.statusOrder"
                      :key="step"
                    >
                      <div
                        class="relative flex items-start gap-4 pb-8 last:pb-0"
                      >
                        <!-- Connecting Line -->
                        <div
                          x-show="index < $store.app.statusOrder.length - 1"
                          class="absolute left-4 top-8 w-0.5 h-8 transition-colors duration-300"
                          :class="isStepCompleted($store.app.selectedVehicle?.Status, step) ? 'bg-emerald-400' : 'bg-slate-200'"
                        ></div>

                        <!-- Status Circle -->
                        <div
                          class="relative z-10 w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 shadow-sm"
                          :class="{
                         'bg-emerald-500 text-white': isStepCompleted($store.app.selectedVehicle?.Status, step),
                         'bg-blue-500 text-white': $store.app.selectedVehicle?.Status === step,
                         'bg-slate-300 text-slate-500': !isStepCompleted($store.app.selectedVehicle?.Status, step) && $store.app.selectedVehicle?.Status !== step
                     }"
                        >
                          <!-- Completed Check -->
                          <svg
                            x-show="isStepCompleted($store.app.selectedVehicle?.Status, step)"
                            class="w-4 h-4"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clip-rule="evenodd"
                            />
                          </svg>
                          <!-- Current Step Dot -->
                          <div
                            x-show="$store.app.selectedVehicle?.Status === step"
                            class="w-2 h-2 bg-white rounded-full animate-pulse"
                          ></div>
                          <!-- Future Step Dot -->
                          <div
                            x-show="!isStepCompleted($store.app.selectedVehicle?.Status, step) && $store.app.selectedVehicle?.Status !== step"
                            class="w-2 h-2 bg-slate-400 rounded-full"
                          ></div>
                        </div>

                        <!-- Step Content -->
                        <div class="flex-1 min-w-0">
                          <p
                            class="font-semibold text-base leading-tight transition-colors duration-300"
                            :class="{
                           'text-emerald-700': isStepCompleted($store.app.selectedVehicle?.Status, step),
                           'text-blue-600': $store.app.selectedVehicle?.Status === step,
                           'text-slate-400': !isStepCompleted($store.app.selectedVehicle?.Status, step) && $store.app.selectedVehicle?.Status !== step
                       }"
                            x-text="$store.app.t(step.toLowerCase().replace(/ /g, '_') + '_fr')"
                          ></p>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>

                <!-- Compact Documents -->
                <div class="card p-6">
                  <h3 class="font-bold text-lg text-slate-800 mb-4">
                    Documents
                  </h3>
                  <div class="space-y-3">
                    <div class="flex justify-between items-center">
                      <span class="text-slate-500 text-sm">BL</span>
                      <span
                        class="font-mono font-bold text-slate-700 text-sm"
                        x-text="$store.app.selectedVehicle?.referBL || 'N/A'"
                      ></span>
                    </div>
                    <div class="flex justify-between items-center">
                      <span class="text-slate-500 text-sm">Voyage</span>
                      <span
                        class="font-bold text-slate-700 text-sm"
                        x-text="$store.app.selectedVehicle?.voyage"
                      ></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

  <!-- View 4: Marketplace -->
  <div
  x-show="$store.app.currentView === 'marketplace'"
  x-data="marketplace"
  x-transition:enter="transition ease-out duration-300"
  x-transition:enter-start="opacity-0"
  x-transition:enter-end="opacity-100"
  class="h-full"
>
  <!-- Header -->
  <header class="mb-8">
    <h1
      class="text-3xl lg:text-4xl font-extrabold text-slate-800 tracking-tight"
      x-text="$store.app.t('marketplace_title')"
    ></h1>
    <p
      class="text-slate-500 mt-2"
      x-text="$store.app.t('marketplace_subtitle')"
    ></p>
  </header>

  <!-- Filters Section -->
  <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 mb-6">
    <!-- Filter Controls -->
    <div class="flex items-center gap-4 mb-6">
      <!-- Brand Filter Button -->
      <div class="flex-1">
        <button
          @click="showBrandModal = true"
          class="w-full px-6 py-4 border-2 border-slate-300 rounded-xl bg-white hover:bg-slate-50 hover:border-blue-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 flex items-center justify-between text-left min-h-[64px]"
        >
          <span class="flex items-center gap-3">
            <img
              x-show="selectedBrand !== 'all'"
              :src="getBrandLogo(selectedBrand)"
              class="w-8 h-8 object-contain"
              @error="$event.target.style.display='none'"
            />
            <span>
              <div class="text-sm font-medium text-slate-600" x-text="$store.app.t('filter_by_brand')"></div>
              <div class="font-semibold text-lg text-slate-800" x-text="selectedBrand === 'all' ? $store.app.t('all_brands') : selectedBrand"></div>
            </span>
          </span>
          <svg class="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
      </div>

      <!-- Model Filter Button -->
      <div class="flex-1">
        <button
          @click="showModelModal = true"
          :disabled="selectedBrand === 'all'"
          class="w-full px-6 py-4 border-2 border-slate-300 rounded-xl bg-white hover:bg-slate-50 hover:border-blue-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 flex items-center justify-between text-left disabled:opacity-50 disabled:cursor-not-allowed min-h-[64px]"
        >
          <span>
            <div class="text-sm font-medium text-slate-600" x-text="$store.app.t('filter_by_model')"></div>
            <div class="font-semibold text-lg text-slate-800" x-text="selectedModel === 'all' ? $store.app.t('all_models') : selectedModel"></div>
          </span>
          <svg class="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
      </div>

      <!-- Sort Buttons -->
      <div class="flex-1">
        <div class="text-sm font-medium text-slate-600 mb-2" x-text="$store.app.t('sort_by')"></div>
        <div class="flex gap-2">
          <button
            @click="sortBy = 'recent'"
            class="flex-1 px-4 py-3 rounded-xl border-2 transition-all duration-200 text-center font-semibold"
            :class="sortBy === 'recent' ? 'border-blue-500 bg-blue-500 text-white shadow-md' : 'border-slate-300 bg-white text-slate-700 hover:bg-slate-50'"
          >
            <span x-text="$store.app.t('latest') || 'Latest'"></span>
          </button>
          <button
            @click="sortBy = sortBy === 'price_low' ? 'price_high' : 'price_low'"
            class="flex-1 px-4 py-3 rounded-xl border-2 transition-all duration-200 text-center font-semibold flex items-center justify-center gap-2"
            :class="sortBy === 'price_low' || sortBy === 'price_high' ? 'border-blue-500 bg-blue-500 text-white shadow-md' : 'border-slate-300 bg-white text-slate-700 hover:bg-slate-50'"
          >
            <span x-text="$store.app.t('price') || 'Price'"></span>
            <svg class="w-4 h-4 transition-transform duration-200" :class="{'rotate-180': sortBy === 'price_high'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    </div>

  <!-- Brand Selection Modal -->
  <div
    x-show="showBrandModal"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
    @click.self="showBrandModal = false"
    @keydown.escape.window="showBrandModal = false"
  >
    <div class="bg-white rounded-3xl shadow-2xl p-8 w-[900px] max-w-[90vw] max-h-[80vh] overflow-hidden">
      <!-- Modal Header -->
      <div class="flex items-center justify-between mb-8">
        <h2 class="text-3xl font-bold text-gray-800" x-text="$store.app.t('choose_brand')"></h2>
        <button
          @click="showBrandModal = false"
          class="w-12 h-12 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center text-gray-500 hover:text-gray-700 transition-all duration-200"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Brand Grid -->
      <div class="grid grid-cols-6 gap-4 max-h-[500px] overflow-y-auto" style="scrollbar-width: thin;">
        <!-- All Brands Option -->
        <button
          @click="selectBrand('all'); showBrandModal = false"
          class="p-6 rounded-2xl border-2 transition-all duration-200 hover:shadow-lg hover:scale-105 flex flex-col items-center gap-3 aspect-square"
          :class="selectedBrand === 'all' ? 'border-blue-600 bg-blue-100 shadow-lg' : 'border-gray-300 bg-white hover:bg-gray-50 shadow-md'"
        >
          <div class="w-16 h-16 bg-gray-200 rounded-xl flex items-center justify-center" :class="selectedBrand === 'all' ? 'bg-blue-200' : 'bg-gray-200'">
            <svg class="w-8 h-8" :class="selectedBrand === 'all' ? 'text-blue-700' : 'text-gray-600'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012 2v2M7 7h10"></path>
            </svg>
          </div>
          <span class="text-sm font-bold" :class="selectedBrand === 'all' ? 'text-blue-800' : 'text-gray-700'" x-text="$store.app.t('all_brands')"></span>
        </button>

        <!-- Brand Options -->
        <template x-for="brand in brands.slice(1)" :key="brand">
          <button
            @click="selectBrand(brand); showBrandModal = false"
            class="p-6 rounded-2xl border-2 transition-all duration-200 hover:shadow-lg hover:scale-105 flex flex-col items-center gap-3 aspect-square"
            :class="selectedBrand === brand ? 'border-blue-600 bg-blue-100 shadow-lg' : 'border-gray-300 bg-white hover:bg-gray-50 shadow-md'"
          >
            <div class="w-16 h-16 rounded-xl flex items-center justify-center p-2" :class="selectedBrand === brand ? 'bg-blue-50' : 'bg-gray-50'">
              <img
                :src="getBrandLogo(brand)"
                :alt="brand"
                class="w-full h-full object-contain"
                @error="$event.target.src='./assets/images/logos/default.png'"
              />
            </div>
            <span class="text-sm font-bold" :class="selectedBrand === brand ? 'text-blue-800' : 'text-gray-700'" x-text="brand"></span>
          </button>
        </template>
      </div>
    </div>
  </div>

  <!-- Model Selection Modal -->
  <div
    x-show="showModelModal && selectedBrand !== 'all'"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
    @click.self="showModelModal = false"
    @keydown.escape.window="showModelModal = false"
  >
    <div class="bg-white rounded-3xl shadow-2xl p-8 w-[700px] max-w-[90vw] max-h-[70vh] overflow-hidden">
      <!-- Modal Header -->
      <div class="flex items-center justify-between mb-8">
        <h2 class="text-3xl font-bold text-gray-800" x-text="($store.app.t('choose_model_for') || 'Choose model for') + ' ' + selectedBrand"></h2>
        <button
          @click="showModelModal = false"
          class="w-12 h-12 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center text-gray-500 hover:text-gray-700 transition-all duration-200"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Model Grid -->
      <div class="grid grid-cols-4 gap-4 max-h-[400px] overflow-y-auto" style="scrollbar-width: thin;">
        <template x-for="model in availableModels" :key="model">
          <button
            @click="selectModel(model); showModelModal = false"
            class="p-6 rounded-xl border-2 transition-all duration-200 hover:shadow-lg hover:scale-105 text-center aspect-[4/3] flex items-center justify-center"
            :class="selectedModel === model ? 'border-blue-600 bg-blue-100 shadow-lg' : 'border-gray-300 bg-white hover:bg-gray-50 shadow-md'"
          >
            <span class="text-lg font-bold" :class="selectedModel === model ? 'text-blue-800' : 'text-gray-700'" x-text="model === 'all' ? ($store.app.t('all_models') || 'All Models') : model"></span>
          </button>
        </template>
      </div>
    </div>
  </div>

  <!-- Results Summary & Active Filters -->
  <div class="bg-slate-50 rounded-xl p-4 mb-6 flex items-center justify-between">
    <div class="flex items-center gap-4">
      <div class="text-slate-700">
        <span class="font-bold text-2xl text-slate-800" x-text="totalTrucks"></span>
        <span class="font-medium text-lg" x-text="totalTrucks === 1 ? $store.app.t('vehicle_found') : $store.app.t('vehicles_found')"></span>
      </div>
      <div x-show="totalPages > 1" class="text-slate-600 font-medium border-l border-slate-300 pl-4">
        <span x-text="$store.app.t('page')"></span>
        <span class="font-bold text-slate-800" x-text="currentPage"></span>
        <span x-text="$store.app.t('of')"></span>
        <span class="font-bold text-slate-800" x-text="totalPages"></span>
      </div>
    </div>
    <div x-show="hasActiveFilters" class="flex items-center gap-3">
      <span class="text-sm font-medium text-slate-600" x-text="$store.app.t('active_filters') || 'Active filters:'"></span>
      <template x-if="selectedBrand !== 'all'">
        <div class="flex items-center gap-2 px-3 py-2 bg-blue-100 text-blue-800 rounded-lg border border-blue-200">
          <img
          :src="getBrandLogo(selectedBrand)"
          :alt="selectedBrand"
          class="w-5 h-5 object-contain"
          @error="$event.target.style.visibility='hidden'; $event.target.style.width='0px'"
          @load="$event.target.style.visibility='visible'; $event.target.style.width='auto'"
        />
          <span class="text-sm font-medium" x-text="selectedBrand"></span>
          <button @click="clearBrandFilter()" class="ml-1 hover:bg-blue-200 rounded-full p-0.5 transition-colors">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </template>
      <template x-if="selectedModel !== 'all' && selectedBrand !== 'all'">
        <div class="flex items-center gap-2 px-3 py-2 bg-blue-100 text-blue-800 rounded-lg border border-blue-200">
          <span class="text-sm font-medium" x-text="selectedModel"></span>
          <button @click="clearModelFilter()" class="ml-1 hover:bg-blue-200 rounded-full p-0.5 transition-colors">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </template>
      <button 
        @click="clearAllFilters()"
        class="text-sm text-red-600 hover:text-red-700 font-medium hover:underline transition-colors"
        x-text="$store.app.t('clear_all') || 'Clear all'"
      ></button>
    </div>
  </div>

  <!-- Truck Grid -->
  <div class="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
    <template x-for="truck in paginatedTrucks" :key="truck.id">
      <div
        @click="showTruckDetails(truck)"
        class="bg-white rounded-2xl shadow-sm border border-slate-200 hover:shadow-xl hover:border-blue-300 hover:scale-105 transition-all duration-300 cursor-pointer group overflow-hidden touch-manipulation flex flex-col h-full"
      >
        <!-- Truck Image -->
        <div class="relative h-52 bg-slate-100 overflow-hidden flex-shrink-0">
          <img
            src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='300'%3E%3Crect width='100%25' height='100%25' fill='%23f1f5f9'/%3E%3C/svg%3E"
            :data-src="truck.images && truck.images[0] ? truck.images[0] : 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop'"
            :alt="truck.brand + ' ' + truck.model"
            class="w-full h-full object-cover transition-all duration-300 group-hover:scale-110"
            @error="$event.target.src='https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop'"
            x-init="observeImage($el)"
          />

          <!-- Brand Logo -->
          <div class="absolute top-4 left-4 w-12 h-12 bg-white rounded-xl shadow-lg flex items-center justify-center">
            <img
              :src="getBrandLogo(truck.brand)"
              :alt="truck.brand"
              class="w-8 h-8 object-contain"
              @error="$event.target.style.display='none'"
            />
          </div>
        </div>

        <!-- Truck Info -->
        <div class="p-6 flex flex-col flex-1">
          <div class="flex-1">
            <h3 class="font-bold text-xl text-slate-800 mb-1 leading-tight" x-text="truck.brand + ' ' + truck.model"></h3>
            <p class="text-sm text-slate-500 mb-3" x-text="truck.designation"></p>

            <!-- Price -->
            <div class="text-2xl font-bold text-blue-600 mb-4" x-text="formatPrice(truck.price)"></div>

            <!-- Specs -->
            <div class="space-y-3 text-sm text-slate-600 mb-5">
              <div class="flex justify-between items-center py-1">
                <span class="font-medium" x-text="$store.app.t('fuel_type')"></span>
                <span class="font-semibold text-slate-800" x-text="truck.fuelType"></span>
              </div>
              <div class="flex justify-between items-center py-1">
                <span class="font-medium" x-text="$store.app.t('availability')"></span>
                <span class="font-semibold text-emerald-600" x-text="truck.availability"></span>
              </div>
            </div>
          </div>

          <!-- View Details Button - Always at bottom -->
          <div class="mt-auto pt-4 border-t border-slate-100">
            <div class="text-center text-sm font-medium text-blue-600 group-hover:text-blue-700 flex items-center justify-center gap-2">
              <span x-text="$store.app.t('view_details')"></span>
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>



  <!-- Pagination Controls -->
  <div x-show="totalPages > 1" class="mt-8 bg-white rounded-2xl shadow-sm border border-slate-200 p-6">
    <div class="flex flex-col lg:flex-row items-center justify-between gap-6">

      <!-- Items Per Page Selector -->
      <div class="flex items-center gap-3">
        <span class="text-slate-600 font-medium" x-text="$store.app.t('show')"></span>
        <div class="flex bg-slate-100 rounded-xl p-1">
          <template x-for="option in itemsPerPageOptions" :key="option">
            <button
              @click="changeItemsPerPage(option)"
              class="px-4 py-2 rounded-lg font-medium transition-all duration-200 min-w-[60px] touch-manipulation"
              :class="itemsPerPage === option ? 'bg-white text-blue-600 shadow-sm font-bold' : 'text-slate-600 hover:text-slate-800'"
              x-text="option"
            ></button>
          </template>
        </div>
        <span class="text-slate-600 font-medium" x-text="$store.app.t('per_page')"></span>
      </div>

      <!-- Page Navigation -->
      <div class="flex flex-col sm:flex-row items-center gap-4">
        <!-- Current Results Info -->
        <div class="text-slate-600 font-medium text-center">
          <span x-text="$store.app.t('showing')"></span>
          <span class="font-bold text-slate-800" x-text="paginationInfo.startItem"></span>-<span class="font-bold text-slate-800" x-text="paginationInfo.endItem"></span>
          <span x-text="$store.app.t('of')"></span>
          <span class="font-bold text-slate-800" x-text="paginationInfo.totalItems"></span>
        </div>

        <!-- Navigation Buttons -->
        <div class="flex items-center gap-2">
          <!-- Previous Button -->
          <button
            @click="prevPage()"
            :disabled="currentPage === 1"
            class="w-12 h-12 rounded-xl border-2 border-slate-300 bg-white flex items-center justify-center transition-all duration-200 touch-manipulation hover:shadow-md"
            :class="currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:border-blue-400 hover:text-blue-600'"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>

          <!-- Page Numbers -->
          <div class="flex items-center gap-1">
            <template x-for="page in visiblePageNumbers" :key="page">
              <button
                @click="goToPage(page)"
                class="w-12 h-12 rounded-xl font-bold transition-all duration-200 touch-manipulation"
                :class="currentPage === page
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'bg-white border-2 border-slate-300 text-slate-700 hover:border-blue-400 hover:text-blue-600 hover:shadow-md'"
                x-text="page"
              ></button>
            </template>
          </div>

          <!-- Next Button -->
          <button
            @click="nextPage()"
            :disabled="currentPage === totalPages"
            class="w-12 h-12 rounded-xl border-2 border-slate-300 bg-white flex items-center justify-center transition-all duration-200 touch-manipulation hover:shadow-md"
            :class="currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:border-blue-400 hover:text-blue-600'"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Marketplace Detail Modal -->
  <div
    x-show="showDetailModal"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm"
    @click.self="closeDetailModal()"
    @keydown.escape.window="closeDetailModal()"
  >
    <!-- Modal Content -->
    <div 
      class="relative w-[95vw] h-[90vh] max-w-none bg-white rounded-2xl shadow-2xl overflow-hidden flex"
      x-show="selectedTruck"
    >
      <!-- Close Button -->
      <button
        @click="closeDetailModal()"
        class="absolute top-6 right-6 z-30 w-16 h-16 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl flex items-center justify-center text-slate-600 hover:text-slate-800 hover:bg-white transition-all duration-200 hover:scale-105 border border-gray-200"
      >
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>

      <!-- Left Side - Image Gallery (68%) -->
      <div class="w-[68%] bg-gradient-to-br from-gray-50 to-gray-100 relative flex flex-col h-full">
        <!-- Main Image Container -->
        <div 
          class="flex-1 relative p-4 min-h-0"
          x-data="{
            startX: 0,
            currentX: 0,
            isDragging: false,
            swipeThreshold: 100,
            dragDistance: 0,
            touchStartTime: 0
          }"
          @touchstart.passive="
            if (selectedTruck?.images?.length > 1) {
              startX = $event.touches[0].clientX;
              touchStartTime = Date.now();
              isDragging = false;
              dragDistance = 0;
            }
          "
          @touchmove.passive="
            if (selectedTruck?.images?.length > 1 && startX !== 0) {
              currentX = $event.touches[0].clientX;
              dragDistance = Math.abs(startX - currentX);
              if (dragDistance > 10) {
                isDragging = true;
              }
            }
          "
          @touchend="
            if (selectedTruck?.images?.length > 1 && startX !== 0) {
              const touchDuration = Date.now() - touchStartTime;
              const deltaX = startX - currentX;
              
              // Only process as swipe if it was a quick gesture with sufficient distance
              if (isDragging && touchDuration < 500 && Math.abs(deltaX) > swipeThreshold) {
                if (deltaX > 0) {
                  nextImage();
                } else {
                  prevImage();
                }
              }
            }
            
            // Reset all values
            isDragging = false;
            startX = 0;
            currentX = 0;
            dragDistance = 0;
            touchStartTime = 0;
          "
          style="touch-action: pan-y;"
        >
          <!-- Main Image -->
          <div class="h-full flex items-center justify-center">
            <img
              :src="selectedTruck?.images?.[currentImageIndex] || 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop'"
              :alt="(selectedTruck?.brand || '') + ' ' + (selectedTruck?.model || '')"
              class="max-w-full max-h-full object-contain rounded-xl shadow-lg"
              @error="$event.target.src='https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop'"
              draggable="false"
              style="user-select: none;"
            />
          </div>

          <!-- Navigation Arrows -->
          <template x-if="selectedTruck?.images?.length > 1">
            <div>
              <button
                @click.stop="prevImage()"
                class="absolute left-3 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/95 backdrop-blur-sm rounded-full shadow-lg flex items-center justify-center text-gray-700 hover:bg-white transition-all duration-150 z-20"
              >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
              </button>
              <button
                @click.stop="nextImage()"
                class="absolute right-3 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/95 backdrop-blur-sm rounded-full shadow-lg flex items-center justify-center text-gray-700 hover:bg-white transition-all duration-150 z-20"
              >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </button>
            </div>
          </template>

          <!-- Image Counter -->
          <div 
            x-show="selectedTruck?.images?.length > 1" 
            class="absolute top-4 left-4 bg-black/80 backdrop-blur-sm rounded-full px-4 py-2 text-white text-sm font-semibold"
          >
            <span x-text="(currentImageIndex + 1) + ' / ' + (selectedTruck?.images?.length || 0)"></span>
          </div>
        </div>

        <!-- Image Thumbnails - Fixed Height -->
        <div 
          x-show="selectedTruck?.images?.length > 1" 
          class="flex-shrink-0 h-20 p-3 bg-white/50 backdrop-blur-sm border-t border-white/20"
        >
          <div class="h-full flex gap-2 overflow-x-auto" style="scrollbar-width: thin; scrollbar-color: #cbd5e1 transparent;">
            <template x-for="(image, index) in selectedTruck?.images" :key="index">
              <button
                @click="selectImage(index)"
                class="flex-shrink-0 w-16 h-12 rounded-lg overflow-hidden border-2 transition-all duration-150"
                :class="index === currentImageIndex ? 'border-blue-500 shadow-md scale-105' : 'border-white/70 opacity-80 hover:opacity-100'"
              >
                <img
                  :src="image"
                  class="w-full h-full object-cover"
                  @error="$event.target.src='https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop'"
                />
              </button>
            </template>
          </div>
        </div>
      </div>

      <!-- Right Side - Vehicle Details (32%) -->
      <div class="w-[32%] bg-white flex flex-col overflow-hidden">
        <!-- Header Section -->
        <div class="p-5 border-b border-gray-100">
          <h1 class="text-2xl font-bold text-gray-900 mb-1" x-text="selectedTruck?.brand || ''"></h1>
          <p class="text-lg text-gray-600 mb-3" x-text="selectedTruck?.model || ''"></p>
          <div class="text-3xl font-bold text-blue-600 mb-2" x-text="selectedTruck?.price ? formatPrice(selectedTruck.price) : ''"></div>
          <div class="text-sm text-gray-600" x-text="selectedTruck?.designation || ''"></div>
        </div>

        <!-- Specifications Section -->
        <div class="flex-1 p-5 overflow-y-auto">
          <div class="space-y-3">
            <!-- Spec Item -->
            <div class="flex items-center justify-between py-2 border-b border-gray-100">
              <span class="text-gray-600 font-medium text-sm" x-text="$store.app.t('kilometers') || 'Kilométrage'"></span>
              <span class="font-bold text-gray-900 text-sm" x-text="selectedTruck?.kilometers ? formatKilometers(selectedTruck.kilometers) : ''"></span>
            </div>
            
            <div class="flex items-center justify-between py-2 border-b border-gray-100">
              <span class="text-gray-600 font-medium text-sm" x-text="$store.app.t('year') || 'Année'"></span>
              <span class="font-bold text-gray-900 text-sm" x-text="selectedTruck?.year || ''"></span>
            </div>
            
            <div class="flex items-center justify-between py-2 border-b border-gray-100">
              <span class="text-gray-600 font-medium text-sm" x-text="$store.app.t('transmission') || 'Transmission'"></span>
              <span class="font-bold text-gray-900 text-sm" x-text="selectedTruck?.transmission || ''"></span>
            </div>
            
            <div class="flex items-center justify-between py-2 border-b border-gray-100">
              <span class="text-gray-600 font-medium text-sm" x-text="$store.app.t('fuel_type') || 'Carburant'"></span>
              <span class="font-bold text-gray-900 text-sm" x-text="selectedTruck?.fuelType || ''"></span>
            </div>
            
            <div class="flex items-center justify-between py-2 border-b border-gray-100">
              <span class="text-gray-600 font-medium text-sm" x-text="$store.app.t('air_conditioning') || 'Climatisation'"></span>
              <span class="font-bold text-sm" :class="selectedTruck?.airConditioning ? 'text-green-600' : 'text-red-500'" x-text="selectedTruck?.airConditioning ? ($store.app.t('yes') || 'Oui') : ($store.app.t('no') || 'Non')"></span>
            </div>
            
            <div class="flex items-center justify-between py-2 border-b border-gray-100">
              <span class="text-gray-600 font-medium text-sm" x-text="$store.app.t('alarm') || 'Alarme'"></span>
              <span class="font-bold text-sm" :class="selectedTruck?.alarm ? 'text-green-600' : 'text-red-500'" x-text="selectedTruck?.alarm ? ($store.app.t('yes') || 'Oui') : ($store.app.t('no') || 'Non')"></span>
            </div>

            <!-- Status Section -->
            <div class="pt-3">
              <div class="flex items-center justify-between py-2">
                <span class="text-gray-600 font-medium text-sm" x-text="$store.app.t('availability') || 'Disponibilité'"></span>
                <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-bold" x-text="selectedTruck?.availability || ''"></span>
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Button -->
        <div class="p-5 border-t border-gray-100">
          <button
            @click="contactSeller()"
            class="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white py-3 px-6 rounded-xl text-lg font-bold transition-all duration-150 shadow-lg"
          >
            <span x-text="$store.app.t('contact_seller') || 'Contacter le vendeur'"></span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>      

           <!-- View 4: Departures -->
           <div
           x-show="['departuresList', 'destinationDepartures'].includes($store.app.currentView)"
           x-data="departures"
           class="h-full"
           x-transition:enter="transition ease-out duration-300"
           x-transition:enter-start="opacity-0"
           x-transition:enter-end="opacity-100"
         >
           <div x-show="$store.app.currentView === 'departuresList'" class="pb-8">
   <header class="mb-8">
     <h1
       class="text-3xl lg:text-4xl font-extrabold text-slate-800 tracking-tight"
       x-text="$store.app.t('departures_title')"
     ></h1>
     <p
       class="text-slate-500 mt-2"
       x-text="$store.app.t('departures_subtitle')"
     ></p>
   </header>
   
   <div class="grid grid-cols-5 gap-4">
     <template x-for="dest in destinations" :key="dest.name">
       <div
         @click="showDestination(dest)"
         x-data="{ count: getVoyageCount(dest.name) }"
         class="bg-white rounded-xl p-4 border border-slate-200 shadow-md hover:shadow-xl hover:border-blue-300 hover:-translate-y-1 transition-all duration-300 cursor-pointer group flex flex-col"
       >
         <!-- Header with flag and destination info -->
         <div class="flex items-start gap-3 mb-3">
           <img
             :src="getCountryFlag(dest.name)"
             class="w-8 h-6 rounded overflow-hidden flex-shrink-0 object-cover"
             :alt="dest.country + ' flag'"
           />
           <div class="flex-1 min-w-0">
             <h3 
               class="font-bold text-lg text-slate-800 leading-tight truncate"
               x-text="dest.name"
             ></h3>
             <p 
               class="text-sm text-slate-500 truncate"
               x-text="dest.country"
             ></p>
           </div>
         </div>
         
         <!-- Footer with departure count and arrow -->
         <div class="flex items-end justify-between mt-auto pt-2">
           <div class="flex items-baseline gap-2">
             <span 
               class="text-2xl font-bold text-blue-600"
               x-text="count"
             ></span>
             <span 
               class="text-sm text-blue-600 font-medium"
               x-text="pluralize(count, 'voyage_single', 'voyage_plural')"
             ></span>
           </div>
           <span class="text-blue-600 group-hover:translate-x-0.5 transition-transform duration-200 text-xl">&rarr;</span>
         </div>
       </div>
     </template>
   </div>
 </div>
         <!-- START: MODIFIED DEPARTURE DETAILS VIEW -->
         <div 
             x-show="$store.app.currentView === 'destinationDepartures'" 
             class="flex flex-col h-full bg-white rounded-2xl shadow-lg overflow-hidden" 
             x-transition:enter="transition ease-out duration-300" 
             x-transition:enter-start="opacity-0" 
             x-transition:enter-end="opacity-100"
             x-cloak
         >
             <div class="grid grid-cols-1 lg:grid-cols-3 h-full">
                 <!-- Left Panel: List of Voyages -->
                 <div class="lg:col-span-1 bg-slate-50/50 border-r border-slate-200 flex flex-col p-6">
                     <header class="flex-shrink-0 mb-6">
                       <button @click="backToDeparturesList()" class="bg-white text-slate-700 hover:bg-slate-100 transition-all shadow-sm border border-slate-200 hover:shadow-md px-4 py-2 rounded-lg flex items-center gap-2 text-sm font-medium mb-4">
                         <i class='bx bx-chevron-left text-lg -ml-1'></i> <span x-text="$store.app.t('back_to_list')"></span>
                     </button>
                     <div>
                         <h1 class="text-3xl font-bold text-slate-800" x-text="selectedDestination?.name"></h1>
                         <p class="text-slate-500 font-medium flex items-center gap-2">
                             <i class='bx bx-map'></i> <span x-text="selectedDestination?.country"></span>
                         </p>
                     </div>
                 </header>
                 <div class="space-y-3 overflow-y-auto flex-1 -mr-3 pr-3">
                     <template x-for="dep in departuresData[selectedDestination?.name]" :key="dep.ship + dep.loadingDate">
                          <div @click="activeDeparture = dep" 
                               class="flex items-center p-4 rounded-xl cursor-pointer border transition-all duration-200"
                               :class="activeDeparture?.ship === dep.ship 
                               ? 'bg-blue-50 border-blue-400 shadow-md' 
                               : 'border-slate-200 bg-white hover:border-blue-300 hover:shadow-sm hover:-translate-y-0.5'">
                             <div class="flex flex-col items-center justify-center text-center w-16 flex-shrink-0 mr-4 pr-4 border-r border-slate-200">
                                 <p class="text-xs font-semibold text-slate-500" x-text="formatDate(dep.loadingDate, { month: 'short' }).toUpperCase()"></p>
                                 <p class="text-3xl font-bold tracking-tight" :class="activeDeparture?.ship === dep.ship ? 'text-blue-600' : 'text-slate-800'" x-text="formatDate(dep.loadingDate, { day: 'numeric' })"></p>
                             </div>
                             <div class="flex-1">
                                 <h3 class="font-bold text-slate-800" x-text="dep.ship"></h3>
                                 <p class="text-sm text-slate-500 mt-1 flex items-center gap-1.5"><i class='bx bx-time-five'></i> <span x-text="$store.app.t('transit_duration') + ': ' + calculateVoyageDays(dep.loadingDate, dep.arrivalDate) + ' ' + $store.app.t('days')"></span></p>
                             </div>
                         </div>
                     </template>
                 </div>
             </div>
             
             <!-- Right Panel: Voyage Details Timeline -->
             <div class="lg:col-span-2 p-6 lg:p-10 flex flex-col bg-slate-100/70">
                     <template x-if="activeDeparture">
                         <div :key="activeDeparture.ship" class="flex flex-col flex-1"
                             x-transition:enter="transition ease-out duration-300"
                             x-transition:enter-start="opacity-0 transform -translate-y-3"
                             x-transition:enter-end="opacity-100 transform translate-y-0"
                             x-transition:leave="transition ease-in duration-200 absolute"
                             x-transition:leave-start="opacity-100 transform translate-y-0"
                             x-transition:leave-end="opacity-0 transform translate-y-3">
                             
                             <!-- Ship Header -->
                             <div class="flex items-center gap-5 mb-8 lg:mb-12">
                                 <div class="w-16 h-16 bg-white rounded-full flex items-center justify-center shadow-md border border-slate-200">
                                     <i class='bx bxs-ship text-3xl text-blue-500'></i>
                                 </div>
                                 <div>
                                     <h2 class="text-3xl font-bold text-slate-800" x-text="activeDeparture.ship"></h2>
                                     <p class="text-slate-500 font-medium" x-text="activeDeparture.loadingPort + ' → ' + activeDeparture.destinationName"></p>
                                 </div>
                             </div>
 
                             <!-- Journey Timeline -->
                             <div class="flex-1 flex items-center justify-center w-full">
                                 <div class="flex items-stretch justify-center w-full gap-4 lg:gap-6">
                                     
                                     <!-- Departure Point -->
                                     <div class="flex-1 bg-white border border-slate-200 p-5 rounded-2xl shadow-sm flex flex-col justify-between">
                                         <div class="flex justify-between items-center mb-5">
                                             <h3 class="font-semibold text-sm text-slate-500 uppercase tracking-wider" x-text="$store.app.t('departure')"></h3>
                                             <div class="w-12 h-12 rounded-full bg-slate-100 flex items-center justify-center text-blue-500 text-2xl"><i class='bx bx-anchor'></i></div>
                                         </div>
                                         <div class="space-y-3">
                                             <p class="font-medium text-slate-800 flex items-center gap-2"><i class='bx bxs-map-pin text-blue-500'></i><span x-text="activeDeparture.loadingPort"></span></p>
                                             <p class="text-sm text-slate-500 flex items-center gap-2"><i class='bx bxs-calendar text-blue-500'></i><span x-text="formatDate(activeDeparture.loadingDate, { day: 'numeric', month: 'long', year: 'numeric' })"></span></p>
                                         </div>
                                     </div>
 
                                     <!-- Center Path & Duration -->
                                     <div class="flex flex-col items-center justify-center flex-shrink-0 w-32 lg:w-40">
                                         <div class="w-full h-1 bg-slate-300 rounded-full relative mb-4">
                                             <div class="absolute top-0 left-0 h-full bg-blue-500 rounded-full w-full"></div>
                                         </div>
                                         <div class="bg-white border border-slate-200 shadow-sm rounded-lg p-2 flex items-center gap-2">
                                             <i class='bx bx-time-five text-blue-500 text-xl'></i>
                                             <div class="text-center leading-tight">
                                                 <span class="font-bold text-lg text-slate-800" x-text="calculateVoyageDays(activeDeparture.loadingDate, activeDeparture.arrivalDate)"></span>
                                                 <span class="text-xs text-slate-500 block" x-text="$store.app.t('days')"></span>
                                             </div>
                                         </div>
                                     </div>
 
                                     <!-- Arrival Point -->
                                     <div class="flex-1 bg-white border border-slate-200 p-5 rounded-2xl shadow-sm flex flex-col justify-between text-right">
                                         <div class="flex justify-between items-center mb-5 flex-row-reverse">
                                             <h3 class="font-semibold text-sm text-slate-500 uppercase tracking-wider" x-text="$store.app.t('arrival')"></h3>
                                             <div class="w-12 h-12 rounded-full bg-slate-100 flex items-center justify-center text-blue-500 text-2xl"><i class='bx bxs-flag-checkered'></i></div>
                                         </div>
                                         <div class="space-y-3 flex flex-col items-end">
                                             <p class="font-medium text-slate-800 flex items-center gap-2 flex-row-reverse"><i class='bx bxs-map-pin text-blue-500'></i><span x-text="activeDeparture.destinationName"></span></p>
                                             <p class="text-sm text-slate-500 flex items-center gap-2 flex-row-reverse"><i class='bx bxs-calendar text-blue-500'></i><span x-text="formatDate(activeDeparture.arrivalDate, { day: 'numeric', month: 'long', year: 'numeric' })"></span></p>
                                         </div>
                                     </div>
                                 </div>
                             </div>
                         </div>
                     </template>
                     <template x-if="!activeDeparture">
                          <div class="text-center text-slate-400 h-full flex flex-col items-center justify-center">
                             <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1"><path stroke-linecap="round" stroke-linejoin="round" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" /></svg>
                             <p class="font-semibold" x-text="$store.app.t('select_voyage_prompt')"></p>
                         </div>
                     </template>
                 </div>
             </div>
         </div>
         <!-- END: MODIFIED DEPARTURE DETAILS VIEW -->
         </div>    

        <!-- View 5: Help Screen -->
        <div
          x-show="$store.app.currentView === 'help'"          
          x-transition:enter="transition ease-out duration-300"
          x-transition:enter-start="opacity-0"
          x-transition:enter-end="opacity-100"
        >
          <header class="text-center mb-10">
            <h1
              class="text-4xl font-extrabold text-slate-800 tracking-tight"
              x-text="$store.app.t('help_main_title')"
            ></h1>
            <p
              class="text-lg text-slate-500 mt-2"
              x-text="$store.app.t('help_main_subtitle')"
            ></p>
          </header>

          <!-- Main Content Grid -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Left Column: Mobile App & FAQ -->
            <div class="space-y-8">
              <!-- Mobile App Card -->
              <div class="card p-8">
                <div class="flex items-center gap-4 mb-6">
                  <div
                    class="w-14 h-14 bg-blue-100 text-blue-600 rounded-2xl flex items-center justify-center"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-7 w-7"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h2
                      class="text-2xl font-bold text-slate-800"
                      x-text="$store.app.t('help_mobile_title')"
                    ></h2>
                    <p
                      class="text-slate-500"
                      x-text="$store.app.t('help_mobile_subtitle')"
                    ></p>
                  </div>
                </div>

                <!-- Dual QR Code Layout -->
                <div class="grid grid-cols-2 gap-6 text-center">
                  <!-- App Store (iOS) -->
                  <div
                    class="bg-slate-50 p-6 rounded-2xl border border-slate-200"
                  >
                    <h3
                      class="font-bold text-slate-800 flex items-center justify-center gap-2"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          d="M10.721 2.222A.75.75 0 0111.45 2.96l.006.007c.023.024.049.046.078.067.142.102.304.19.49.262.28.108.599.168.94.168.75 0 1.43-.399 1.836-1.028a.75.75 0 011.385.553c-.56 1.51-1.996 2.549-3.721 2.549-1.298 0-2.45-.682-3.108-1.731-.646-1.03-1.077-2.25-1.077-3.513 0-.13.003-.26.01-.39a.75.75 0 01.75-.742.75.75 0 01.742.75c0 1.05-.378 2.06-1.03 2.918.291-.56.685-1.03 1.152-1.378a.75.75 0 01.97.222z"
                        />
                        <path
                          fill-rule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm.328-11.013a.75.75 0 01.742.75v3.496c0 .19-.072.37-.202.508l-2.09 2.314a.75.75 0 11-1.118-1.004l1.63-1.802v-3.262a.75.75 0 01.75-.742z"
                          clip-rule="evenodd"
                        />
                      </svg>
                      App Store
                    </h3>
                    <div
                      class="my-4 p-2 bg-white rounded-lg inline-block shadow-inner"
                    >
                      <!-- IMPORTANT: Replace with your actual App Store QR code -->
                      <img
                        src="https://api.qrserver.com/v1/create-qr-code/?size=140x140&data=https://apps.apple.com/us/app/your-app-name"
                        alt="App Store QR Code"
                        class="rounded-md"
                      />
                    </div>
                    <p
                      class="text-sm text-slate-500"
                      x-text="$store.app.t('help_mobile_scan_ios')"
                    ></p>
                  </div>
                  <!-- Google Play (Android) -->
                  <div
                    class="bg-slate-50 p-6 rounded-2xl border border-slate-200"
                  >
                    <h3
                      class="font-bold text-slate-800 flex items-center justify-center gap-2"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M18.928 9.389C18.783 8.81 18.237 8 17.25 8c-.92 0-1.632.482-2.134 1.011l-.898.901 1.013 1.013.844.836c.5.5 1.18.5 1.633-.05.452-.552.71-1.28.517-2.312zm-3.04.183c-.34-.34-.783-.534-1.253-.534-.9 0-1.716.51-2.115 1.252l-5.116 9.364a1.25 1.25 0 001.07 1.837h9.362a1.25 1.25 0 001.07-1.837l-2.92-5.361-1.1-1.721z"
                          clip-rule="evenodd"
                        />
                      </svg>
                      Google Play
                    </h3>
                    <div
                      class="my-4 p-2 bg-white rounded-lg inline-block shadow-inner"
                    >
                      <!-- IMPORTANT: Replace with your actual Google Play QR code -->
                      <img
                        src="https://api.qrserver.com/v1/create-qr-code/?size=140x140&data=https://play.google.com/store/apps/details?id=com.yourapp.id"
                        alt="Google Play QR Code"
                        class="rounded-md"
                      />
                    </div>
                    <p
                      class="text-sm text-slate-500"
                      x-text="$store.app.t('help_mobile_scan_android')"
                    ></p>
                  </div>
                </div>
              </div>

              <!-- FAQ Card -->
              <div class="card p-8">
                <h2
                  class="text-2xl font-bold text-slate-800 mb-6"
                  x-text="$store.app.t('help_faq_title')"
                ></h2>
                <div class="space-y-6">
                  <div>
                    <h4
                      class="font-semibold text-slate-700"
                      x-text="$store.app.t('help_faq_q1')"
                    ></h4>
                    <p
                      class="text-slate-500 mt-1 text-sm"
                      x-text="$store.app.t('help_faq_a1')"
                    ></p>
                  </div>
                  <div>
                    <h4
                      class="font-semibold text-slate-700"
                      x-text="$store.app.t('help_faq_q2')"
                    ></h4>
                    <p
                      class="text-slate-500 mt-1 text-sm"
                      x-text="$store.app.t('help_faq_a2')"
                    ></p>
                  </div>
                  <div>
                    <h4
                      class="font-semibold text-slate-700"
                      x-text="$store.app.t('help_faq_q3')"
                    ></h4>
                    <p
                      class="text-slate-500 mt-1 text-sm"
                      x-text="$store.app.t('help_faq_a3')"
                    ></p>
                  </div>
                  <div>
                    <h4
                      class="font-semibold text-slate-700"
                      x-text="$store.app.t('help_faq_q4')"
                    ></h4>
                    <p
                      class="text-slate-500 mt-1 text-sm"
                      x-text="$store.app.t('help_faq_a4')"
                    ></p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Right Column: Contact Info Card -->
            <div class="card p-8">
              <h2
                class="text-2xl font-bold text-slate-800 mb-8"
                x-text="$store.app.t('help_contact_title')"
              ></h2>
              <div class="space-y-8">
                <!-- Phone Contact -->
                <div class="flex items-start gap-5">
                  <div
                    class="w-12 h-12 bg-emerald-100 text-emerald-600 rounded-xl flex-shrink-0 flex items-center justify-center"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3
                      class="text-lg font-bold text-slate-700"
                      x-text="$store.app.t('help_contact_phone_title')"
                    ></h3>
                    <div class="mt-2 space-y-2">
                      <div>
                        <p
                          class="font-semibold text-slate-600"
                          x-text="$store.app.t('help_contact_phone_client')"
                        ></p>
                        <p class="text-slate-500">+33 1 40 20 30 40</p>
                        <p
                          class="text-xs text-slate-400"
                          x-text="$store.app.t('help_contact_phone_hours')"
                        ></p>
                      </div>
                      <div>
                        <p
                          class="font-semibold text-slate-600"
                          x-text="$store.app.t('help_contact_phone_emergency')"
                        ></p>
                        <p class="text-slate-500">+33 6 80 90 10 20</p>
                        <p
                          class="text-xs text-slate-400"
                          x-text="$store.app.t('help_contact_phone_availability')"
                        ></p>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Email Contact -->
                <div class="flex items-start gap-5">
                  <div
                    class="w-12 h-12 bg-sky-100 text-sky-600 rounded-xl flex-shrink-0 flex items-center justify-center"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3
                      class="text-lg font-bold text-slate-700"
                      x-text="$store.app.t('help_contact_email_title')"
                    ></h3>
                    <div class="mt-2 space-y-2">
                      <div>
                        <p
                          class="font-semibold text-slate-600"
                          x-text="$store.app.t('help_contact_email_support')"
                        ></p>
                        <p class="text-slate-500"><EMAIL></p>
                      </div>
                      <div>
                        <p
                          class="font-semibold text-slate-600"
                          x-text="$store.app.t('help_contact_email_commercial')"
                        ></p>
                        <p class="text-slate-500">
                          <EMAIL>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Address -->
                <div class="flex items-start gap-5">
                  <div
                    class="w-12 h-12 bg-fuchsia-100 text-fuchsia-600 rounded-xl flex-shrink-0 flex items-center justify-center"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      />
                      <path d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3
                      class="text-lg font-bold text-slate-700"
                      x-text="$store.app.t('help_contact_address_title')"
                    ></h3>
                    <div class="mt-2">
                      <p class="font-semibold text-slate-600">
                        SOCAR Maritime Services
                      </p>
                      <p class="text-slate-500">
                        125 Avenue des Champs-Élysées<br />75008 Paris, France
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </main>
    </div>

    <!-- Your Application's Modular JavaScript -->
    <script src="./src/main.js" type="module"></script>

    <!-- Alpine.js Core -->
    <script
      defer
      src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"
    ></script>
  </body>
</html>
