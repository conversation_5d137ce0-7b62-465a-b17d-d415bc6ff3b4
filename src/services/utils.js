import { destinations } from '../config/departuresData.js';

export function getBrandLogo(desc) {
  const brand = desc ? desc.split(" ")[0].toLowerCase() : "default"; // default logo name
  const path = `./assets/images/logos/${brand}.png`;
  // Fallback to a default logo if a specific brand logo doesn't exist.
  // Create a 'default.png' in your logos folder (e.g., a simple SOCAR logo).
  const fallbackPath = "./assets/images/logos/default.png";

  // In the HTML, you will use it like this:
  // <img :src="getBrandLogo(vehicle.description)" @error="$event.target.src = './assets/images/logos/default.png'">
  // The @error attribute is a robust way to handle missing images.

  return path;
}

export function getCountryFlag(destination) {
  const destinationObj = destinations.find(dest => 
    dest.name.toUpperCase() === destination?.toUpperCase()
  );
  const code = destinationObj?.code || "xx";
  return `https://flagcdn.com/w40/${code}.png`;
}
export function formatDate(dateString, options) {
  if (!dateString) return "";
  const date = new Date(dateString);
  // Note: We access the store via `this` from inside an Alpine component
  return date.toLocaleDateString(this.$store.app.currentLang, {
    ...options,
    timeZone: "UTC",
  });
}
export function parseConsignee(consigneeStr) {
  if (!consigneeStr) return { name: "N/A", extraInfo: null };
  const match = consigneeStr.match(/\(([^)]+)\)/);
  if (match) {
    const name = consigneeStr.substring(0, match.index).trim();
    const extraInfo = match[1].trim();
    return { name, extraInfo };
  }
  return { name: consigneeStr.trim(), extraInfo: null };
}
